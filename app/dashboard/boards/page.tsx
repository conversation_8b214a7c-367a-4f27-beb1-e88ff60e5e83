'use client';

import React from 'react';
import { BoardsStats } from '@/components/boards/boards-stats';
import { BoardsTable } from '@/components/boards/boards-table';

export default function BoardsPage() {
  const [refreshTrigger, setRefreshTrigger] = React.useState(0);

  const handleDataChange = React.useCallback(() => {
    // Increment the trigger to refresh stats
    setRefreshTrigger((prev) => prev + 1);
  }, []);

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div>
        <h1 className="font-semibold text-3xl tracking-tight">Job Boards</h1>
        <p className="text-muted-foreground">
          Configure and manage your automated job posting boards with advanced
          filtering and Airtable integration.
        </p>
      </div>

      <BoardsStats refreshTrigger={refreshTrigger} />
      <BoardsTable onDataChange={handleDataChange} />
    </div>
  );
}
