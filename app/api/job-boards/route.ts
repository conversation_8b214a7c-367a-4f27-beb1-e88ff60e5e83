import { type NextRequest, NextResponse } from 'next/server';
import {
  createJ<PERSON><PERSON>oard,
  deleteJobBoard,
  getJobBoardConfigs,
  type JobBoardConfig,
  updateJobBoard,
} from '@/lib/job-board-service';
import { getAirtablePat } from '@/lib/secrets-manager';
import { logger } from '@/lib/utils';
import {
  validateAirtableBaseId,
  validateAirtablePat,
  validateAirtableTableName,
  validatePatWithError,
} from '@/lib/validation';

/**
 * Test Airtable connection by making a request to the connection test endpoint
 */
async function testAirtableConnection(config: {
  pat: string;
  baseId: string;
  tableName: string;
}): Promise<{ success: boolean; error?: string; details?: unknown }> {
  try {
    const response = await fetch(
      `${
        process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
      }/api/airtable-test`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ airtableConfig: config }),
      }
    );

    const result = await response.json();
    return result;
  } catch (error) {
    return {
      success: false,
      error: 'Failed to test connection',
      details: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// GET /api/job-boards - List all job board configurations
export async function GET() {
  try {
    const configs = await getJobBoardConfigs();

    return NextResponse.json({
      success: true,
      boards: configs,
      count: configs.length,
    });
  } catch (error) {
    logger.error('Error fetching job board configs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch job board configurations' },
      { status: 500 }
    );
  }
}

// POST /api/job-boards - Create a new job board configuration
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();

    // Validate required fields
    const requiredFields = ['id', 'name', 'airtable', 'posting'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    if (!body.airtable.baseId) {
      return NextResponse.json(
        { error: 'Missing required field: airtable.baseId' },
        { status: 400 }
      );
    }

    // Validate Airtable configuration format
    const validationErrors: string[] = [];

    if (!validateAirtableBaseId(body.airtable.baseId)) {
      validationErrors.push(
        'Base ID format is invalid (must be "app" + 14 alphanumeric characters)'
      );
    }

    const tableName = body.airtable.tableName || 'Jobs';
    if (!validateAirtableTableName(tableName)) {
      validationErrors.push('Table name is invalid (must be 1-100 characters)');
    }

    if (validationErrors.length > 0) {
      return NextResponse.json(
        {
          error: 'Invalid Airtable configuration',
          validationErrors,
          examples: {
            baseId: 'apprhCjWTxfG3JX5p',
            tableName: 'Jobs',
          },
        },
        { status: 400 }
      );
    }

    const config: Omit<JobBoardConfig, 'lastPostedAt' | 'totalPosted'> = {
      id: body.id,
      name: body.name,
      description: body.description || '',
      enabled: body.enabled ?? true,
      airtable: {
        baseId: body.airtable.baseId,
        tableName: body.airtable.tableName || 'Jobs',
        pat: body.airtable.pat,
      },
      posting: {
        dailyLimit: body.posting.dailyLimit || 10,
        strategy: body.posting.strategy || 'newest_first',
        avoidRepostingDays: body.posting.avoidRepostingDays || 30,
        postingTimes: body.posting.postingTimes,
        timezone: body.posting.timezone,
      },
      filters: body.filters || {},
    };

    // MANDATORY: Test Airtable connection before creating the board
    const pat =
      config.airtable.pat ||
      (await getAirtablePat(config.id)) ||
      process.env.AIRTABLE_PAT;

    if (!pat) {
      return NextResponse.json(
        {
          error: 'PAT token is required',
          details:
            'Provide airtable.pat in the request or configure it via /api/airtable-secrets',
        },
        { status: 400 }
      );
    }

    const patValidation = validatePatWithError(pat);
    if (!patValidation.valid) {
      return NextResponse.json(
        {
          error: 'Invalid PAT token format',
          details: patValidation.error,
        },
        { status: 400 }
      );
    }

    const connectionTest = await testAirtableConnection({
      pat,
      baseId: config.airtable.baseId,
      tableName: config.airtable.tableName,
    });

    if (!connectionTest.success) {
      return NextResponse.json(
        {
          error: 'Airtable connection test failed',
          details: connectionTest.error,

          help: 'Please verify your Airtable base ID, table name, and PAT token are correct and that you have write access to the table.',
        },
        { status: 400 }
      );
    }

    // Verify write permissions
    const permissions = (connectionTest as any).permissions;
    if (permissions && !permissions.write) {
      return NextResponse.json(
        {
          error: 'Insufficient Airtable permissions',
          details:
            'Write access is required to post jobs to this Airtable base',
          writeError: permissions.writeError,
        },
        { status: 403 }
      );
    }

    const success = await createJobBoard(config);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to create job board configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Job board '${config.name}' created successfully`,
      id: config.id,
    });
  } catch (error) {
    logger.error('Error creating job board config:', error);
    return NextResponse.json(
      { error: 'Failed to create job board configuration' },
      { status: 500 }
    );
  }
}

// PUT /api/job-boards?id=board-id - Update a job board configuration
export async function PUT(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const boardId = url.searchParams.get('id');

    if (!boardId) {
      return NextResponse.json(
        { error: 'Missing board ID parameter' },
        { status: 400 }
      );
    }

    const body = await req.json();
    const success = await updateJobBoard(boardId, body);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to update job board configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Job board '${boardId}' updated successfully`,
    });
  } catch (error) {
    logger.error('Error updating job board config:', error);
    return NextResponse.json(
      { error: 'Failed to update job board configuration' },
      { status: 500 }
    );
  }
}

// PATCH /api/job-boards?id=board-id - Update a job board configuration (partial update)
export async function PATCH(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const boardId = url.searchParams.get('id');

    if (!boardId) {
      return NextResponse.json(
        { error: 'Missing board ID parameter' },
        { status: 400 }
      );
    }

    const body = await req.json();
    const success = await updateJobBoard(boardId, body);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to update job board configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Job board '${boardId}' updated successfully`,
    });
  } catch (error) {
    logger.error('Error updating job board config:', error);
    return NextResponse.json(
      { error: 'Failed to update job board configuration' },
      { status: 500 }
    );
  }
}

// DELETE /api/job-boards?id=board-id - Delete a job board configuration
export async function DELETE(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const boardId = url.searchParams.get('id');

    if (!boardId) {
      return NextResponse.json(
        { error: 'Missing board ID parameter' },
        { status: 400 }
      );
    }

    const success = await deleteJobBoard(boardId);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete job board configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Job board '${boardId}' deleted successfully`,
    });
  } catch (error) {
    logger.error('Error deleting job board config:', error);
    return NextResponse.json(
      { error: 'Failed to delete job board configuration' },
      { status: 500 }
    );
  }
}
