import { type NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { createHealthCheckResponse } from '@/lib/api-utils';
import { mapWorkableFields, validateJob } from '@/lib/job-validation';

// WorkableJobData interface for type safety
interface WorkableJobData {
  title?: string;
  company?: string;
  description?: string;
  url?: string;
  reference_number?: string;
  [key: string]: unknown;
}

import { insertJobsToDatabase } from '@/lib/database-utils';
import { logger } from '@/lib/logger';

/**
 * Workable Webhook Handler - Simplified Architecture
 *
 * Fast & simple flow:
 * 1. Receive webhook from Apify
 * 2. Fetch jobs from dataset
 * 3. Minimal extraction & direct storage to Supabase
 * 4. Return immediately (< 200ms)
 *
 * Processing happens async via QStash scheduled jobs
 */

// Database-only deduplication using external_id uniqueness

// Apify webhook schema
const ApifyWebhookSchema = z
  .object({
    eventType: z.enum(['ACTOR.RUN.SUCCEEDED', 'ACTOR.RUN.FAILED']),
    eventData: z.object({
      actorRunId: z.string(),
    }),
    resource: z
      .object({
        id: z.string(),
        status: z.string(),
        defaultDatasetId: z.string(),
        actorId: z.string().optional(),
        actId: z.string().optional(),
      })
      .passthrough(),
  })
  .passthrough();

/**
 * Fetches jobs from Apify dataset using dataset ID
 */
async function fetchJobsFromDataset(datasetId: string): Promise<unknown[]> {
  const apifyToken = process.env.APIFY_TOKEN;
  if (!apifyToken) {
    throw new Error('APIFY_TOKEN not configured');
  }

  const datasetUrl = `https://api.apify.com/v2/datasets/${datasetId}/items?format=json&clean=true`;
  const response = await fetch(datasetUrl, {
    headers: { Authorization: `Bearer ${apifyToken}` },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch dataset: ${response.status}`);
  }

  const jobs = await response.json();

  if (!Array.isArray(jobs)) {
    throw new Error('Invalid dataset response format');
  }

  return jobs;
}

/**
 * Processes and validates Workable jobs for database insertion
 */
function processWorkableJobs(jobs: unknown[]): {
  jobsToInsert: Record<string, unknown>[];
  skippedCount: number;
} {
  const jobsToInsert: Record<string, unknown>[] = [];
  let skippedCount = 0;

  for (const job of jobs) {
    // Map Workable fields to our database structure
    const mappedJob = mapWorkableFields(job as WorkableJobData);

    // Validate job and generate external ID
    const validation = validateJob(mappedJob, 'workable');

    if (!validation.isValid) {
      logger.warn(
        `Skipping invalid job: ${validation.missingFields.join(', ')}`,
        {
          jobId: (job as WorkableJobData).reference_number,
          title: (job as WorkableJobData).title,
        }
      );
      skippedCount++;
      continue;
    }

    // Add external ID and prepare for database
    const dbJob = {
      ...mappedJob,
      external_id: validation.externalId,

      // Complete raw data storage for AI processing
      raw_sourced_job_data: job, // Store complete original job structure

      // System fields
      source_type: 'workable',
      source_name: 'Workable',
      processing_status: 'pending',
      sourced_at: new Date().toISOString(),
    };

    jobsToInsert.push(dbJob);
  }

  return { jobsToInsert, skippedCount };
}

// insertJobsToDatabase function now imported from @/lib/database-utils

export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    logger.info('🔔 Workable webhook received');

    // Check for Apify webhook header
    const apifyHeader = request.headers.get('x-apify-webhook');
    if (!apifyHeader) {
      logger.warn('Missing X-Apify-Webhook header');
    }

    // Parse webhook payload
    const body = await request.json();
    const webhook = ApifyWebhookSchema.parse(body);

    // Only process successful runs
    if (webhook.eventType !== 'ACTOR.RUN.SUCCEEDED') {
      return NextResponse.json(
        {
          message: 'Ignoring non-success event',
          eventType: webhook.eventType,
        },
        { status: 200 }
      );
    }

    const runId = webhook.resource.id;
    const datasetId = webhook.resource.defaultDatasetId;
    const actorId = webhook.resource.actorId || webhook.resource.actId;

    logger.info('Processing Workable actor run', {
      runId,
      datasetId,
      actorId,
    });

    // Fetch jobs from Apify dataset
    const jobs = await fetchJobsFromDataset(datasetId);

    if (jobs.length === 0) {
      logger.info('No jobs found in dataset', { datasetId });
      return NextResponse.json(
        {
          success: true,
          message: 'No jobs to process',
          jobsReceived: 0,
          jobsSaved: 0,
        },
        { status: 200 }
      );
    }

    logger.info(`📥 Received ${jobs.length} jobs from Workable`);

    // Process and validate jobs
    const { jobsToInsert, skippedCount } = processWorkableJobs(jobs);

    // Insert jobs to database
    const savedCount = await insertJobsToDatabase(jobsToInsert);

    const processingTime = Date.now() - startTime;

    logger.info('✅ Workable webhook completed', {
      runId,
      jobsReceived: jobs.length,
      jobsSaved: savedCount,
      jobsSkipped: skippedCount,
      processingTime: `${processingTime}ms`,
    });

    // Track pipeline metrics
    logger.pipeline({
      step: 'STORED',
      source: 'workable',
      jobCount: savedCount,
      success: true,
      duration: processingTime,
      metadata: {
        apifyRunId: runId,
        apifyDatasetId: datasetId,
        jobsSkipped: skippedCount,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Workable webhook processed successfully',
      runId,
      jobsReceived: jobs.length,
      jobsSaved: savedCount,
      jobsSkipped: skippedCount,
      processingTime: `${processingTime}ms`,
    });
  } catch (error) {
    logger.error('Workable webhook failed', { error });

    // Always return 200 to Apify to prevent retries
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Webhook acknowledged but processing failed',
        processingTime: `${Date.now() - startTime}ms`,
      },
      { status: 200 }
    );
  }
}

// Health check endpoint
export function GET() {
  return createHealthCheckResponse({
    service: 'Workable Webhook',
    architecture: 'Simplified Direct Insert',
    features: [
      'Direct Supabase insertion',
      'Database deduplication',
      'Fast response (< 200ms)',
      'Async AI processing',
      'Always returns 200 to Apify',
    ],
    environment: {
      hasApifyToken: !!process.env.APIFY_TOKEN,
      hasSupabaseConfig: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
    },
  });
}
