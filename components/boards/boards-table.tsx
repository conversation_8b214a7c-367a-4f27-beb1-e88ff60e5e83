'use client';

import {
  type ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  type SortingState,
  useReactTable,
  type VisibilityState,
} from '@tanstack/react-table';
import { Plus, RefreshCw } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { DataTablePagination } from '../jobs/data-table-pagination';
import { DataTableViewOptions } from '../jobs/data-table-view-options';
import { createBoardsColumns, type JobBoard } from './columns';
import { CreateBoardDialog } from './create-board-dialog';
import { EditBoardDialog } from './edit-board-dialog';
import { PatManagementDialog } from './pat-management-dialog';

interface BoardsTableProps {
  onDataChange?: () => void; // Callback to notify parent of data changes
}

export function BoardsTable({ onDataChange }: BoardsTableProps) {
  const [boards, setBoards] = useState<JobBoard[]>([]);
  const [loading, setLoading] = useState(true);
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'name', desc: false },
  ]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});

  // Modal states
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showPatDialog, setShowPatDialog] = useState(false);
  const [selectedBoard, setSelectedBoard] = useState<JobBoard | null>(null);

  // PAT management state
  const [patManagement, setPatManagement] = useState<{
    availablePats: Record<string, string[]>;
    storageMethod: string;
  } | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Helper function to process boards with PAT status
  const processBoardsWithPatStatus = useCallback(
    (
      boardsData: any[],
      patData: { availablePats: Record<string, string[]> } | null
    ): JobBoard[] => {
      return boardsData.map((board: any) => ({
        ...board,
        patStatus: (patData?.availablePats[board.id]
          ? 'configured'
          : 'missing') as 'configured' | 'missing',
      }));
    },
    []
  );

  // Unified data loading function that loads both boards and PAT data
  const loadAllData = useCallback(async () => {
    console.log('🔄 loadAllData called - loading boards and PAT data');
    setLoading(true);
    setError(null);

    try {
      // Load both datasets in parallel
      const [boardsResponse, patResponse] = await Promise.all([
        fetch('/api/job-boards'),
        fetch('/api/airtable-secrets'),
      ]);

      if (!boardsResponse.ok) {
        throw new Error(`Failed to load boards: ${boardsResponse.status}`);
      }
      if (!patResponse.ok) {
        throw new Error(`Failed to load PAT data: ${patResponse.status}`);
      }

      const [boardsData, patData] = await Promise.all([
        boardsResponse.json(),
        patResponse.json(),
      ]);

      // Extract boards array from response
      const boardsArray = Array.isArray(boardsData)
        ? boardsData
        : boardsData.boards || [];

      // Process boards with correct PAT status from the start
      const boardsWithPatStatus = processBoardsWithPatStatus(
        boardsArray,
        patData.success ? patData : null
      );

      // Update state atomically
      console.log('✅ Processed boards with PAT status:', boardsWithPatStatus);
      setBoards(boardsWithPatStatus);
      setPatManagement(patData.success ? patData : null);
    } catch (error) {
      console.error('Failed to load data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load data');
      setBoards([]);
      setPatManagement(null);
    } finally {
      setLoading(false);
    }
  }, [processBoardsWithPatStatus]);

  // Legacy load boards function (kept for backward compatibility)
  const loadBoards = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/job-boards');
      if (!response.ok) {
        throw new Error(`Failed to load job boards: ${response.status}`);
      }

      const data = await response.json();
      const boardsData = Array.isArray(data) ? data : data.boards || [];

      // Transform boards with default PAT status (will be updated by useEffect)
      // biome-ignore lint/suspicious/noExplicitAny: API response structure varies and needs dynamic property access
      const transformedBoards: JobBoard[] = boardsData.map((board: any) => ({
        ...board,
        patStatus: 'missing' as 'configured' | 'missing',
      }));

      setBoards(transformedBoards);
    } catch (error) {
      toast.error('Failed to load job boards', {
        description: error instanceof Error ? error.message : 'Unknown error',
      });
      setBoards([]);
    } finally {
      setLoading(false);
    }
  }, [patManagement]);

  // Load PAT management info
  const loadPatManagement = useCallback(async () => {
    try {
      const response = await fetch('/api/airtable-secrets');
      if (response.ok) {
        const data = await response.json();
        setPatManagement({
          availablePats: data.availablePats || {},
          storageMethod: data.storageMethod || 'encrypted_db',
        });
      }
    } catch (_error) {
      // Silently ignore PAT management errors - not critical for board functionality
    }
  }, []);

  // biome-ignore lint/suspicious/noExplicitAny: Job board configuration can have dynamic structure from form data
  const createJobBoard = async (config: any) => {
    try {
      const response = await fetch('/api/job-boards', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config),
      });

      if (!response.ok) {
        throw new Error(`Failed to create job board: ${response.status}`);
      }

      await loadAllData();
      onDataChange?.(); // Notify parent of data change
      setShowCreateDialog(false);
      toast.success('Job board created successfully');
    } catch (error) {
      toast.error('Failed to create job board', {
        description: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Update job board
  const updateJobBoard = useCallback(
    async (updatedBoard: JobBoard) => {
      try {
        const response = await fetch(`/api/job-boards?id=${updatedBoard.id}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updatedBoard),
        });

        if (!response.ok) {
          throw new Error(`Failed to update job board: ${response.status}`);
        }

        await loadAllData();
        onDataChange?.(); // Notify parent of data change
        toast.success('Job board updated successfully');
      } catch (error) {
        toast.error('Failed to update job board', {
          description: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    },
    [loadAllData, onDataChange]
  );

  // Delete job board
  const deleteJobBoard = useCallback(
    async (id: string) => {
      if (
        !confirm(
          'Are you sure you want to delete this job board? This action cannot be undone.'
        )
      ) {
        return;
      }

      try {
        const response = await fetch(`/api/job-boards?id=${id}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error(`Failed to delete job board: ${response.status}`);
        }

        await loadAllData();
        onDataChange?.(); // Notify parent of data change
        toast.success('Job board deleted successfully');
      } catch (error) {
        toast.error('Failed to delete job board', {
          description: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    },
    [loadAllData, onDataChange]
  );

  // Toggle job board enabled status
  const toggleEnabled = useCallback(
    async (board: JobBoard) => {
      const updatedBoard = { ...board, enabled: !board.enabled };
      await updateJobBoard(updatedBoard);
    },
    [updateJobBoard]
  );

  // Store PAT for board
  const storePat = async (boardId: string, pat: string) => {
    try {
      const response = await fetch('/api/airtable-secrets', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ boardId, pat }),
      });

      if (!response.ok) {
        throw new Error(`Failed to store PAT: ${response.status}`);
      }

      await loadAllData();
      onDataChange?.(); // Notify parent of data change
      setShowPatDialog(false);
      setSelectedBoard(null);
      toast.success('PAT stored successfully');
    } catch (error) {
      toast.error('Failed to store PAT', {
        description: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Test connection
  const testConnection = useCallback(async (board: JobBoard) => {
    if (board.patStatus !== 'configured') {
      toast.error('Cannot test connection', {
        description: 'PAT not configured for this board',
      });
      return;
    }

    const loadingToast = toast.loading(
      `Testing connection to ${board.name}...`
    );

    try {
      const response = await fetch('/api/airtable-test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          baseId: board.airtable.baseId,
          tableName: board.airtable.tableName,
          boardId: board.id,
        }),
      });

      toast.dismiss(loadingToast);

      if (response.ok) {
        const data = await response.json();

        // Enhanced success message with performance info (record count removed as it may be inaccurate due to view filters)
        const responseTime = data.performance?.responseTime || 'unknown';
        const tableName = data.config?.tableName || board.airtable.tableName;

        toast.success('Connection test successful!', {
          description: `Connected to ${tableName} table (${responseTime})`,
          duration: 5000, // Show longer to give time to read the details
        });
      } else {
        const errorData = await response.json();
        toast.error('Connection test failed', {
          description: errorData.error || `Status ${response.status}`,
          duration: 6000, // Show error longer
        });
      }
    } catch (error) {
      toast.dismiss(loadingToast);
      toast.error('Connection test failed', {
        description: error instanceof Error ? error.message : 'Unknown error',
        duration: 6000,
      });
    }
  }, []);

  // Action handlers
  const handleEdit = useCallback((board: JobBoard) => {
    setSelectedBoard(board);
    setShowEditDialog(true);
  }, []);

  const handleManagePat = useCallback((board: JobBoard) => {
    setSelectedBoard(board);
    setShowPatDialog(true);
  }, []);

  const boardsColumns = useMemo(
    () =>
      createBoardsColumns(
        handleEdit,
        toggleEnabled,
        deleteJobBoard,
        handleManagePat,
        testConnection
      ),
    [deleteJobBoard, handleEdit, handleManagePat, testConnection, toggleEnabled]
  );

  const table = useReactTable({
    data: boards,
    columns: boardsColumns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  });

  // Load all data on component mount
  useEffect(() => {
    loadAllData();
  }, [loadAllData]);

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="h-8 w-48 animate-pulse rounded bg-muted" />
          <div className="h-10 w-32 animate-pulse rounded bg-muted" />
        </div>
        <div className="rounded-md border">
          <div className="h-[400px] w-full animate-pulse bg-muted" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="font-bold text-2xl tracking-tight">Job Boards</h2>
          <Button onClick={loadAllData} size="sm" variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Retry
          </Button>
        </div>
        <div className="rounded-md border border-destructive/50 bg-destructive/10 p-6 text-center">
          <p className="text-destructive text-sm">
            Failed to load job boards: {error}
          </p>
          <Button
            className="mt-4"
            onClick={loadAllData}
            size="sm"
            variant="outline"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with search and actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Input
            className="max-w-sm"
            onChange={(event) =>
              table.getColumn('name')?.setFilterValue(event.target.value)
            }
            placeholder="Filter boards..."
            value={(table.getColumn('name')?.getFilterValue() as string) ?? ''}
          />
          <Button onClick={loadAllData} size="sm" variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
        <div className="flex items-center space-x-2">
          <DataTableViewOptions table={table} />
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Job Board
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  data-state={row.getIsSelected() && 'selected'}
                  key={row.id}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell className="min-w-0" key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  className="h-24 text-center"
                  colSpan={boardsColumns.length}
                >
                  <div className="flex flex-col items-center justify-center space-y-2">
                    <div className="text-2xl">🎯</div>
                    <div className="font-medium text-lg text-muted-foreground">
                      No job boards configured
                    </div>
                    <div className="text-muted-foreground text-sm">
                      Create your first job board to start automatically posting
                      jobs
                    </div>
                    <Button
                      className="mt-2"
                      onClick={() => setShowCreateDialog(true)}
                    >
                      Create Your First Job Board
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <DataTablePagination table={table} />

      {/* Modal Dialogs */}
      <CreateBoardDialog
        onClose={() => setShowCreateDialog(false)}
        onSubmit={createJobBoard}
        open={showCreateDialog}
      />

      {selectedBoard && (
        <>
          <EditBoardDialog
            board={selectedBoard}
            onClose={() => {
              setShowEditDialog(false);
              setSelectedBoard(null);
            }}
            onSubmit={updateJobBoard}
            open={showEditDialog}
          />

          <PatManagementDialog
            board={selectedBoard}
            onClose={() => {
              setShowPatDialog(false);
              setSelectedBoard(null);
            }}
            onSubmit={(pat) => storePat(selectedBoard.id, pat)}
            open={showPatDialog}
          />
        </>
      )}
    </div>
  );
}
